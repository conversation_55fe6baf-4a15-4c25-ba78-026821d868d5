<?php

namespace App\Controllers;

class ApplicationPreScreeningController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = \Config\Services::session();
    }

    /**
     * [GET] Display exercises in selection status for pre-screening context.
     * URI: /application_pre_screening/exercises
     */
    public function exercises()
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load ExerciseModel and get exercises where status != 'draft'
        $exerciseModel = new \App\Models\ExerciseModel();
        $exercises = $exerciseModel->getPreScreeningExercises($orgId);

        $data = [
            'title' => 'Exercises Available for Pre-Screening',
            'menu' => 'applications',
            'exercises' => $exercises
        ];

        return view('application_pre_screening/application_pre_screening_exercise_list', $data);
    }

    /**
     * [GET] Display unique applicants within an exercise for pre-screening.
     * URI: /application_pre_screening/exercise/{exerciseId}/prescreening_applicants
     */
    public function exercisePreScreeningApplicants($exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load models
        $exerciseModel = new \App\Models\ExerciseModel();
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Get exercise details
        $exercise = $exerciseModel->find($exerciseId);
        if (!$exercise || $exercise['org_id'] != $orgId) {
            $this->session->setFlashdata('error', 'Exercise not found or access denied.');
            return redirect()->to(base_url('application_pre_screening'));
        }

        // First, get positions marked for pre-screening for this exercise
        $exercisePositionsPreScreenModel = new \App\Models\ExercisePositionsPreScreenModel();
        $preScreeningPositions = $exercisePositionsPreScreenModel->select('position_id')
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->findAll();

        // Extract position IDs
        $preScreeningPositionIds = array_column($preScreeningPositions, 'position_id');

        // If no positions are marked for pre-screening, return empty array
        if (empty($preScreeningPositionIds)) {
            $applicants = [];
        } else {
            // Get unique applicants for this exercise who have NOT been pre-screened yet
            // Only show applicants who applied for positions marked for pre-screening
            $applicants = $applicationModel->select('
                    appx_application_details.applicant_id,
                    appx_application_details.first_name,
                    appx_application_details.last_name,
                    appx_application_details.email_address,
                    appx_application_details.gender,
                    appx_application_details.contact_details,
                    CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name,
                    COUNT(appx_application_details.id) as application_count
                ')
                ->where('exercise_id', $exerciseId)
                ->where('org_id', $orgId)
                ->whereIn('position_id', $preScreeningPositionIds)
                ->groupBy('applicant_id')
                ->havingGroupStart()
                    ->having('SUM(CASE WHEN pre_screened_at IS NOT NULL THEN 1 ELSE 0 END) = 0')
                    ->having('SUM(CASE WHEN pre_screened_by IS NOT NULL THEN 1 ELSE 0 END) = 0')
                    ->having('SUM(CASE WHEN pre_screened_status IS NOT NULL AND pre_screened_status != "" THEN 1 ELSE 0 END) = 0')
                    ->having('SUM(CASE WHEN pre_screened_remarks IS NOT NULL AND pre_screened_remarks != "" THEN 1 ELSE 0 END) = 0')
                    ->having('SUM(CASE WHEN pre_screened_criteria_results IS NOT NULL AND pre_screened_criteria_results != "" THEN 1 ELSE 0 END) = 0')
                ->havingGroupEnd()
                ->orderBy('full_name', 'ASC')
                ->findAll();
        }

        $data = [
            'title' => 'Applicants in Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'applicants' => $applicants
        ];

        return view('application_pre_screening/exercise_prescreening_applicants_list', $data);
    }

    /**
     * [GET] Display exercises that have pre-screened applicants.
     * URI: /application_pre_screening/prescreened
     */
    public function prescreenedExercises()
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load AppxApplicationDetailsModel and get exercises with pre-screened applicants
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();
        $exercises = $applicationModel->getExercisesWithPreScreenedApplicants($orgId);

        $data = [
            'title' => 'Exercises with Pre-screened Applicants',
            'menu' => 'applications',
            'exercises' => $exercises
        ];

        return view('application_pre_screening/application_pre_screening_prescreened_exercise_list', $data);
    }

    /**
     * [GET] Display pre-screened applicants for a specific exercise.
     * URI: /application_pre_screening/exercise/{exerciseId}/prescreened_applicants
     */
    public function exercisePreScreenedApplicants($exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load models
        $exerciseModel = new \App\Models\ExerciseModel();
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Get exercise details
        $exercise = $exerciseModel->where('id', $exerciseId)
                                 ->where('org_id', $orgId)
                                 ->first();

        if (!$exercise) {
            $this->session->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('application_pre_screening/prescreened'));
        }

        // Get pre-screened applicants for this exercise
        $applicants = $applicationModel->getPreScreenedApplicantsByExercise($exerciseId, $orgId);

        $data = [
            'title' => 'Pre-screened Applicants in Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'applicants' => $applicants
        ];

        return view('application_pre_screening/exercise_prescreened_applicants_list', $data);
    }

    /**
     * [GET] Display pre-screening profile for a specific applicant within an exercise.
     * URI: /application_pre_screening/prescreening_profile/{applicantId}/{exerciseId}
     */
    public function preScreeningProfile($applicantId, $exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load models
        $exerciseModel = new \App\Models\ExerciseModel();
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();
        $applicantModel = new \App\Models\ApplicantsModel();
        $applicationFilesModel = new \App\Models\AppxApplicationFilesModel();

        // Get exercise details
        $exercise = $exerciseModel->find($exerciseId);
        if (!$exercise || $exercise['org_id'] != $orgId) {
            $this->session->setFlashdata('error', 'Exercise not found or access denied.');
            return redirect()->to(base_url('application_pre_screening'));
        }

        // Get applicant basic details
        $applicant = $applicantModel->find($applicantId);
        if (!$applicant) {
            $this->session->setFlashdata('error', 'Applicant not found.');
            return redirect()->to(base_url('application_pre_screening/exercise/' . $exerciseId . '/prescreening_applicants'));
        }

        // Get all applications for this applicant in this exercise with position details
        $applications = $applicationModel->select('
                appx_application_details.*,
                positions.position_reference,
                positions.designation as position_title,
                positions.classification,
                positions.location as position_location,
                positions.annual_salary,
                positions_groups.group_name,
                CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->where('appx_application_details.applicant_id', $applicantId)
            ->where('appx_application_details.exercise_id', $exerciseId)
            ->where('appx_application_details.org_id', $orgId)
            ->orderBy('appx_application_details.created_at', 'DESC')
            ->findAll();

        if (empty($applications)) {
            $this->session->setFlashdata('error', 'No applications found for this applicant in this exercise.');
            return redirect()->to(base_url('application_pre_screening/exercise/' . $exerciseId . '/prescreening_applicants'));
        }

        // Get applicant name from first application (more reliable than applicant table)
        $applicantName = $applications[0]['full_name'];

        // Get all files for this applicant across all applications in this exercise
        $applicantFiles = [];
        $uniqueFiles = []; // Track unique files to avoid duplicates

        foreach ($applications as $application) {
            $files = $applicationFilesModel->getFilesByApplicationId($application['id']);
            foreach ($files as $file) {
                // Create a unique identifier for the file
                // Priority: 1) applicant_file_id, 2) file_path, 3) file_title + applicant_id
                $uniqueKey = '';

                if (!empty($file['applicant_file_id'])) {
                    // Use applicant_file_id as primary unique identifier
                    $uniqueKey = 'file_id_' . $file['applicant_file_id'];
                } elseif (!empty($file['file_path'])) {
                    // Use file_path as secondary unique identifier
                    $uniqueKey = 'file_path_' . $file['file_path'];
                } else {
                    // Fallback to file_title + applicant_id
                    $uniqueKey = 'file_title_' . $file['applicant_id'] . '_' . $file['file_title'];
                }

                // Only add if we haven't seen this unique file before
                if (!isset($uniqueFiles[$uniqueKey])) {
                    $uniqueFiles[$uniqueKey] = true;
                    $applicantFiles[] = $file;
                }
            }
        }

        // Get pre-screening criteria from exercise
        $preScreenCriteria = [];
        if (!empty($exercise['pre_screen_criteria'])) {
            $preScreenCriteria = json_decode($exercise['pre_screen_criteria'], true) ?? [];
        }

        // Get existing pre-screening results for this applicant and exercise
        $existingPreScreening = $applicationModel->select('
                pre_screened_at,
                pre_screened_by,
                pre_screened_status,
                pre_screened_remarks,
                pre_screened_criteria_results
            ')
            ->where('applicant_id', $applicantId)
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->first();

        // Parse existing criteria results if available
        $existingCriteriaResults = [];
        $existingAiAnalysis = [];
        if ($existingPreScreening && !empty($existingPreScreening['pre_screened_criteria_results'])) {
            $combinedData = json_decode($existingPreScreening['pre_screened_criteria_results'], true) ?? [];

            // Extract criteria results (everything except ai_analysis)
            $existingCriteriaResults = $combinedData;
            if (isset($existingCriteriaResults['ai_analysis'])) {
                unset($existingCriteriaResults['ai_analysis']);
            }

            // Extract AI analysis data
            if (isset($combinedData['ai_analysis'])) {
                $existingAiAnalysis = $combinedData['ai_analysis'];
            }
        }

        // Get screened by username if available
        $screenedByUsername = null;
        if ($existingPreScreening && !empty($existingPreScreening['pre_screened_by'])) {
            $usersModel = new \App\Models\UsersModel();
            $screenedByUser = $usersModel->find($existingPreScreening['pre_screened_by']);
            if ($screenedByUser) {
                $screenedByUsername = $screenedByUser['username'];
            }
        }

        $data = [
            'title' => 'Pre-Screening Profile: ' . esc($applicantName),
            'menu' => 'applications',
            'exercise' => $exercise,
            'applicant' => $applicant,
            'applicant_name' => $applicantName,
            'applicant_id' => $applicantId,
            'applications' => $applications,
            'applicant_files' => $applicantFiles,
            'pre_screen_criteria' => $preScreenCriteria,
            'existing_prescreening' => $existingPreScreening,
            'existing_criteria_results' => $existingCriteriaResults,
            'existing_ai_analysis' => $existingAiAnalysis,
            'screened_by_username' => $screenedByUsername
        ];

        return view('application_pre_screening/prescreening_profile', $data);
    }

    /**
     * [GET] Display the detailed view of a specific application for pre-screening.
     * URI: /application_pre_screening/show/{id}
     *
     * @param int $id Application ID
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function show($id)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load models
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Get application with related data
        $application = $applicationModel->getApplicationWithDetails($id, $orgId);

        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        // Extract exercise data from application
        $exercise = [
            'id' => $application['exercise_id'],
            'exercise_name' => $application['exercise_name'],
            'gazzetted_no' => $application['gazzetted_no'],
            'advertisement_no' => $application['advertisement_no'],
            'gazzetted_date' => $application['gazzetted_date'],
            'advertisement_date' => $application['advertisement_date'],
            'is_internal' => $application['is_internal'],
            'mode_of_advertisement' => $application['mode_of_advertisement'],
            'publish_date_from' => $application['publish_date_from'],
            'publish_date_to' => $application['publish_date_to']
        ];

        // Extract position data from application
        $position = [
            'id' => $application['position_id'],
            'position_reference' => $application['position_reference'],
            'designation' => $application['position_title'],
            'classification' => $application['classification'],
            'location' => $application['position_location'],
            'annual_salary' => $application['annual_salary'],
            'qualifications' => $application['qualifications'],
            'knowledge' => $application['knowledge'],
            'skills_competencies' => $application['skills_competencies'],
            'job_experiences' => $application['job_experiences']
        ];

        // Extract pre-screening criteria from exercise if available
        $preScreenCriteria = [];
        if (!empty($application['pre_screen_criteria'])) {
            $preScreenCriteria = json_decode($application['pre_screen_criteria'], true) ?? [];
        }

        $data = [
            'title' => 'Application Pre-Screening',
            'menu' => 'applications',
            'application' => $application,
            'exercise' => $exercise,
            'position' => $position,
            'preScreenCriteria' => $preScreenCriteria
        ];

        return view('application_pre_screening/application_pre_screening_detailed_view', $data);
    }

    /**
     * [POST] Save pre-screening results for an application.
     * URI: /application_pre_screening/save/{id}
     *
     * @param int $id Application ID
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save($id)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load model
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Find the application first to ensure it exists
        $application = $applicationModel->getApplicationWithDetails($id, $orgId);
        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        // Process criteria results
        $criteriaIndices = $this->request->getPost('criteria_indices') ?? [];
        $criteriaMet = $this->request->getPost('criteria_met') ?? [];
        $criteriaRemarks = $this->request->getPost('criteria_remarks') ?? [];

        $criteriaResults = [];
        foreach ($criteriaIndices as $criteriaIndex) {
            $criteriaResults[] = [
                'criteriaIndex' => $criteriaIndex,
                'met' => isset($criteriaMet[$criteriaIndex]) ? true : false,
                'remarks' => trim($criteriaRemarks[$criteriaIndex] ?? '')
            ];
        }

        // Prepare data for update
        $data = [
            'pre_screened_at' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $this->request->getPost('status'),
            'pre_screened_remarks' => trim($this->request->getPost('remarks')),
            'pre_screened_criteria_results' => json_encode($criteriaResults),
            'updated_by' => $this->session->get('user_id')
        ];

        // Update the application
        if ($applicationModel->update($id, $data)) {
            return redirect()->to(base_url('application_pre_screening/show/' . $id))
                ->with('success', 'Pre-screening results saved successfully.');
        } else {
            return redirect()->to(base_url('application_pre_screening/show/' . $id))
                ->with('error', 'Failed to save pre-screening results.');
        }
    }

    /**
     * [POST] Batch pre-screen multiple applications.
     * URI: /application_pre_screening/batch_update
     */
    public function batchUpdate()
    {
        $ids = $this->request->getPost('ids');
        $status = $this->request->getPost('status');
        $remarks = trim($this->request->getPost('remarks') ?? '');

        if (empty($ids) || !is_array($ids)) {
            $this->session->setFlashdata('error', 'No applications selected.');
            return redirect()->back();
        }

        if (empty($status)) {
            $this->session->setFlashdata('error', 'Status is required.');
            return redirect()->back()->withInput();
        }

        if ($status === 'failed' && empty($remarks)) {
             $this->session->setFlashdata('error', 'Remarks are required when batch failing applications.');
            return redirect()->back()->withInput();
        }

        // Load model
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Prepare batch update data
        $data = [
            'pre_screened_at' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $status,
            'pre_screened_remarks' => "[Batch Update] " . $remarks,
            'pre_screened_criteria_results' => null,
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id')
        ];

        $successCount = 0;
        $failCount = 0;

        foreach ($ids as $id) {
            $id = (int) $id;
            if ($id > 0) {
                try {
                    if ($applicationModel->update($id, $data)) {
                        $successCount++;
                    } else {
                        $failCount++;
                    }
                } catch (\Exception $e) {
                    log_message('error', 'Exception during batch pre-screening for ID ' . $id . ': ' . $e->getMessage());
                    $failCount++;
                }
            } else {
                $failCount++;
            }
        }

        // Set Flash Message
        $message = '';
        if ($successCount > 0) {
            $message .= "$successCount application(s) pre-screened successfully. ";
        }
        if ($failCount > 0) {
            $message .= "$failCount application(s) failed to update.";
        }

        if ($successCount > 0) {
            $this->session->setFlashdata('success', $message);
        } else {
            $this->session->setFlashdata('error', $message);
        }

        return redirect()->back();
    }

    /**
     * [GET] Display all applications for a specific applicant within an exercise.
     * URI: /application_pre_screening/applicant_applications/{applicantId}/{exerciseId}
     */
    public function applicantApplications($applicantId, $exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load models
        $exerciseModel = new \App\Models\ExerciseModel();
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Get exercise details
        $exercise = $exerciseModel->find($exerciseId);
        if (!$exercise || $exercise['org_id'] != $orgId) {
            $this->session->setFlashdata('error', 'Exercise not found or access denied.');
            return redirect()->to(base_url('application_pre_screening'));
        }

        // Get all applications for this applicant in this exercise
        $applications = $applicationModel->select('
                appx_application_details.*,
                positions.position_reference,
                positions.designation as position_title,
                positions.classification,
                positions.location as position_location,
                positions_groups.group_name,
                CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->where('appx_application_details.applicant_id', $applicantId)
            ->where('appx_application_details.exercise_id', $exerciseId)
            ->where('appx_application_details.org_id', $orgId)
            ->orderBy('appx_application_details.created_at', 'DESC')
            ->findAll();

        if (empty($applications)) {
            $this->session->setFlashdata('error', 'No applications found for this applicant in this exercise.');
            return redirect()->to(base_url('application_pre_screening/exercise/' . $exerciseId . '/prescreening_applicants'));
        }

        // Get applicant name from first application
        $applicantName = $applications[0]['full_name'];

        $data = [
            'title' => 'Applications by ' . esc($applicantName) . ' in Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'applicant_name' => $applicantName,
            'applicant_id' => $applicantId,
            'applications' => $applications
        ];

        return view('application_pre_screening/applicant_applications_list', $data);
    }

    /**
     * [POST] Save pre-screening results from the prescreening profile page.
     * URI: /application_pre_screening/save_prescreening_results
     */
    public function save_prescreening_results()
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url('application_pre_screening'));
        }

        // Get form data
        $applicantId = $this->request->getPost('applicant_id');
        $exerciseId = $this->request->getPost('exercise_id');
        $status = $this->request->getPost('pre_screened_status');
        $remarks = $this->request->getPost('pre_screened_remarks');
        $aiAnalysis = $this->request->getPost('pre_screened_ai_analysis');
        $criteriaResults = $this->request->getPost('pre_screened_criteria_results');

        // Validate required fields
        if (empty($applicantId) || empty($exerciseId) || empty($status)) {
            $this->session->setFlashdata('error', 'Missing required fields: applicant_id, exercise_id, or status.');
            return redirect()->back()->withInput();
        }

        try {
            // Load model
            $applicationModel = new \App\Models\AppxApplicationDetailsModel();

            // Find ALL applications for this applicant in this exercise
            $applications = $applicationModel->getApplicationsByApplicantAndExercise($applicantId, $exerciseId, $orgId);

            if (empty($applications)) {
                $this->session->setFlashdata('error', 'No applications found for this applicant and exercise.');
                return redirect()->back()->withInput();
            }

            // Check which applications currently have empty prescreening status (for email notification)
            $applicationsNeedingNotification = [];
            foreach ($applications as $application) {
                if (empty($application['pre_screened_status'])) {
                    $applicationsNeedingNotification[] = $application;
                }
            }

            // Combine criteria results and AI analysis into single JSON structure
            $combinedData = [];
            if (!empty($criteriaResults)) {
                $criteriaData = json_decode($criteriaResults, true);
                if ($criteriaData) {
                    $combinedData = $criteriaData;
                }
            }

            // Add AI analysis data to the combined structure
            if (!empty($aiAnalysis)) {
                $aiData = json_decode($aiAnalysis, true);
                if ($aiData) {
                    $combinedData['ai_analysis'] = $aiData;
                }
            }

            // Log the combined data for debugging
            log_message('info', 'Combined prescreening data: ' . json_encode($combinedData));

            // Prepare data for update
            $updateData = [
                'pre_screened_at' => date('Y-m-d H:i:s'),
                'pre_screened_by' => $this->session->get('user_id'),
                'pre_screened_status' => $status,
                'pre_screened_remarks' => trim($remarks ?? ''),
                'pre_screened_criteria_results' => json_encode($combinedData),
                'updated_by' => $this->session->get('user_id')
            ];

            // Start database transaction
            $db = \Config\Database::connect();
            $db->transStart();

            $updatedCount = 0;
            $failedUpdates = [];

            // Update all applications for this applicant in this exercise
            foreach ($applications as $application) {
                if ($applicationModel->update($application['id'], $updateData)) {
                    $updatedCount++;
                } else {
                    $failedUpdates[] = $application['application_number'] ?? $application['id'];
                }
            }

            // Complete transaction
            $db->transComplete();

            if ($db->transStatus() === false || !empty($failedUpdates)) {
                $errorMsg = 'Failed to save pre-screening results to some applications.';
                if (!empty($failedUpdates)) {
                    $errorMsg .= ' Failed applications: ' . implode(', ', $failedUpdates);
                }
                $this->session->setFlashdata('error', $errorMsg);
                $this->session->setFlashdata('validation_errors', $applicationModel->errors());
                return redirect()->back()->withInput();
            } else {
                // Send email notification if there were applications with empty prescreening status
                if (!empty($applicationsNeedingNotification)) {
                    try {
                        $emailService = new \App\Services\EmailService();
                        $applicantEmail = $applicationsNeedingNotification[0]['email_address'];
                        $emailResult = $emailService->sendPrescreeningNotification($applicationsNeedingNotification, $applicantEmail);

                        if (!$emailResult['success']) {
                            log_message('warning', 'Failed to send prescreening notification email: ' . $emailResult['message']);
                        }
                    } catch (\Exception $e) {
                        log_message('error', 'Error sending prescreening notification email: ' . $e->getMessage());
                    }
                }

                $successMsg = "Pre-screening results saved successfully to {$updatedCount} application(s).";
                $this->session->setFlashdata('success', $successMsg);
                return redirect()->to(base_url('application_pre_screening/prescreening_profile/' . $applicantId . '/' . $exerciseId));
            }

        } catch (\Exception $e) {
            log_message('error', 'Error saving pre-screening results: ' . $e->getMessage());
            $this->session->setFlashdata('error', 'An error occurred while saving pre-screening results: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }


}
