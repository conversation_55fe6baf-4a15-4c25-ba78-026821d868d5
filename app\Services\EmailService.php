<?php

namespace App\Services;

use App\Models\DakoiiOrgModel;
use App\Models\PositionsModel;
use App\Models\ExerciseModel;

/**
 * EmailService
 * 
 * Service for sending professional emails with proper templates and organization branding
 */
class EmailService
{
    protected $orgModel;
    protected $positionsModel;
    protected $exerciseModel;

    public function __construct()
    {
        $this->orgModel = new DakoiiOrgModel();
        $this->positionsModel = new PositionsModel();
        $this->exerciseModel = new ExerciseModel();
    }

    /**
     * Send acknowledgment email to applicant
     *
     * @param array $application Application data
     * @param string $applicantEmail Applicant's email address
     * @return array Result with success status and message
     */
    public function sendAcknowledgmentEmail($application, $applicantEmail)
    {
        try {
            if (empty($applicantEmail)) {
                return [
                    'success' => false,
                    'message' => 'No email address provided'
                ];
            }

            // Get related data
            $organization = $this->orgModel->find($application['org_id']);
            $position = $this->positionsModel->find($application['position_id']);
            $exercise = $this->exerciseModel->find($application['exercise_id']);

            if (!$organization || !$position || !$exercise) {
                return [
                    'success' => false,
                    'message' => 'Required data not found for email generation'
                ];
            }

            // Prepare email data
            $emailData = [
                'applicant_name' => trim($application['first_name'] . ' ' . $application['last_name']),
                'application' => $application,
                'organization' => $organization,
                'position' => $position,
                'exercise' => $exercise
            ];

            // Generate email content
            $emailContent = $this->generateEmailContent($emailData);
            
            // Send email
            $result = $this->sendEmail(
                $applicantEmail,
                'Application Receipt Acknowledgment - ' . $application['application_number'],
                $emailContent,
                $organization
            );

            return $result;

        } catch (\Exception $e) {
            log_message('error', 'Email service error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error sending email: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate email content using template
     *
     * @param array $data Email template data
     * @return string Generated HTML content
     */
    private function generateEmailContent($data)
    {
        // Load the email template
        $template = view('emails/acknowledgment_template', $data);
        
        return $template;
    }

    /**
     * Send email using CodeIgniter email service
     *
     * @param string $to Recipient email
     * @param string $subject Email subject
     * @param string $content Email content (HTML)
     * @param array $organization Organization data for reply-to
     * @return array Result with success status
     */
    private function sendEmail($to, $subject, $content, $organization)
    {
        try {
            $emailService = \Config\Services::email();

            // Configure email settings
            $config = [
                'protocol' => 'smtp',
                'SMTPHost' => 'mail.dakoiims.com',
                'SMTPUser' => '<EMAIL>',
                'SMTPPass' => 'dakoiianzii',
                'SMTPPort' => 465,
                'SMTPCrypto' => 'ssl',
                'mailType' => 'html',
                'charset' => 'utf-8',
                'wordWrap' => true,
                'newline' => "\r\n"
            ];

            $emailService->initialize($config);

            // Set sender
            $emailService->setFrom('<EMAIL>', 'DERS - Dakoii Echad Recruitment & Selection System');
            
            // Set reply-to using organization email
            if (!empty($organization['email_addresses'])) {
                $orgEmails = $this->parseEmailAddresses($organization['email_addresses']);
                if (!empty($orgEmails)) {
                    $emailService->setReplyTo($orgEmails[0], $organization['org_name']);
                }
            }

            // Set recipient and content
            $emailService->setTo($to);
            $emailService->setSubject($subject);
            $emailService->setMessage($content);

            // Send email
            $result = $emailService->send();

            if ($result) {
                log_message('info', 'Acknowledgment email sent successfully to: ' . $to);
                return [
                    'success' => true,
                    'message' => 'Email sent successfully'
                ];
            } else {
                $debugInfo = $emailService->printDebugger();
                log_message('error', 'Failed to send email: ' . $debugInfo);
                return [
                    'success' => false,
                    'message' => 'Failed to send email: ' . $debugInfo
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Exception while sending email: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Exception while sending email: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Parse email addresses from organization email field
     *
     * @param string $emailAddresses Comma-separated email addresses
     * @return array Array of email addresses
     */
    private function parseEmailAddresses($emailAddresses)
    {
        if (empty($emailAddresses)) {
            return [];
        }

        // Split by comma and clean up
        $emails = explode(',', $emailAddresses);
        $cleanEmails = [];

        foreach ($emails as $email) {
            $email = trim($email);
            if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $cleanEmails[] = $email;
            }
        }

        return $cleanEmails;
    }

    /**
     * Send prescreening notification email to applicant
     *
     * @param array $applications Array of application data
     * @param string $applicantEmail Applicant's email address
     * @return array Result with success status and message
     */
    public function sendPrescreeningNotification($applications, $applicantEmail)
    {
        try {
            if (empty($applicantEmail) || empty($applications)) {
                return [
                    'success' => false,
                    'message' => 'No email address or applications provided'
                ];
            }

            // Get related data from first application
            $firstApplication = $applications[0];
            $organization = $this->orgModel->find($firstApplication['org_id']);
            $exercise = $this->exerciseModel->find($firstApplication['exercise_id']);

            if (!$organization || !$exercise) {
                return [
                    'success' => false,
                    'message' => 'Required organization or exercise data not found'
                ];
            }

            // Get position details for all applications
            $positions = [];
            foreach ($applications as $application) {
                $position = $this->positionsModel->find($application['position_id']);
                if ($position) {
                    $positions[] = $position;
                }
            }

            if (empty($positions)) {
                return [
                    'success' => false,
                    'message' => 'No position data found for applications'
                ];
            }

            // Prepare email data
            $emailData = [
                'applicant_name' => trim($firstApplication['first_name'] . ' ' . $firstApplication['last_name']),
                'applications' => $applications,
                'organization' => $organization,
                'positions' => $positions,
                'exercise' => $exercise
            ];

            // Generate email content using template
            $emailContent = view('emails/prescreening_notification_template', $emailData);

            // Send email
            $result = $this->sendEmail(
                $applicantEmail,
                'Application Pre-Screening Notification - ' . $exercise['exercise_name'],
                $emailContent,
                $organization
            );

            return $result;

        } catch (\Exception $e) {
            log_message('error', 'Prescreening email service error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error sending prescreening notification: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send test email to verify configuration
     *
     * @param string $testEmail Test email address
     * @return array Result with success status
     */
    public function sendTestEmail($testEmail)
    {
        try {
            $testData = [
                'applicant_name' => 'Test Applicant',
                'application' => [
                    'application_number' => 'TEST001',
                    'created_at' => date('Y-m-d H:i:s')
                ],
                'organization' => [
                    'org_name' => 'Test Organization',
                    'logo_path' => '',
                    'postal_address' => 'Test Address',
                    'phone_numbers' => '+************',
                    'email_addresses' => '<EMAIL>'
                ],
                'position' => [
                    'designation' => 'Test Position',
                    'position_reference' => 'TEST-001'
                ],
                'exercise' => [
                    'gazzetted_no' => 'TEST/2025',
                    'gazzetted_date' => date('Y-m-d'),
                    'advertisement_no' => 'ADV/001/2025',
                    'advertisement_date' => date('Y-m-d'),
                    'is_internal' => 0,
                    'publish_date_from' => date('Y-m-d'),
                    'publish_date_to' => date('Y-m-d', strtotime('+30 days'))
                ]
            ];

            $content = $this->generateEmailContent($testData);
            
            return $this->sendEmail(
                $testEmail,
                'DERS Email Test - ' . date('Y-m-d H:i:s'),
                $content,
                $testData['organization']
            );

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Test email error: ' . $e->getMessage()
            ];
        }
    }



    /**
     * Validate email configuration
     *
     * @return array Validation result
     */
    public function validateEmailConfig()
    {
        $issues = [];

        // Check SMTP settings
        $requiredSettings = [
            'SMTPHost' => 'mail.dakoiims.com',
            'SMTPUser' => '<EMAIL>',
            'SMTPPass' => 'dakoiianzii',
            'SMTPPort' => 465
        ];

        foreach ($requiredSettings as $setting => $value) {
            if (empty($value)) {
                $issues[] = "Missing {$setting} configuration";
            }
        }

        return [
            'valid' => empty($issues),
            'issues' => $issues
        ];
    }
}
