# DERS Notification Emails Features Implementation Guide

## Overview

This guide provides a comprehensive approach for implementing email notification features throughout the DERS (Dakoii Echad Recruitment & Selection System). Based on the successful implementation of the prescreening notification feature, this document outlines the standardized process for adding similar email functionality to other features.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Implementation Strategy](#implementation-strategy)
3. [Step-by-Step Implementation Process](#step-by-step-implementation-process)
4. [Email Template Standards](#email-template-standards)
5. [Common Use Cases](#common-use-cases)
6. [Best Practices](#best-practices)
7. [Testing Guidelines](#testing-guidelines)
8. [Troubleshooting](#troubleshooting)

## Architecture Overview

### Core Components

1. **EmailService** (`app/Services/EmailService.php`)
   - Central service for all email operations
   - Handles SMTP configuration and email sending
   - Contains reusable email methods

2. **Email Templates** (`app/Views/emails/`)
   - Standardized HTML templates with DERS branding
   - Consistent styling and layout
   - Reusable components

3. **Controllers**
   - Trigger email notifications at appropriate business logic points
   - Handle email success/failure gracefully

4. **Models**
   - Provide data for email content
   - Support email-related queries

### Email Flow Architecture

```
Controller Action → Check Conditions → Gather Data → EmailService → Template → SMTP → Recipient
```

## Implementation Strategy

### The "Minimal Code Changes" Approach

**Principle:** Implement email notifications with the fewest possible code changes while maintaining consistency and reliability.

**Benefits:**
- ✅ Quick implementation
- ✅ Easy maintenance
- ✅ Consistent user experience
- ✅ Minimal risk of breaking existing functionality

### Key Design Decisions

1. **Single EmailService:** All email functionality centralized
2. **Template-Based:** Use view templates for consistency
3. **Graceful Failure:** Email failures don't break main functionality
4. **Conditional Sending:** Only send when specific conditions are met
5. **Logging:** All email activities are logged for debugging

## Step-by-Step Implementation Process

### Step 1: Identify the Trigger Point

**Questions to Ask:**
- When should the email be sent?
- What conditions must be met?
- What data is needed for the email?
- Who should receive the email?

**Example Scenarios:**
- Application status changes
- Interview scheduling
- Document uploads
- Deadline reminders
- System notifications

### Step 2: Determine Email Conditions

**Common Patterns:**
```php
// Send only on first occurrence
if (empty($previousStatus) && !empty($newStatus)) {
    // Send notification
}

// Send on specific status changes
if ($oldStatus !== $newStatus && $newStatus === 'target_status') {
    // Send notification
}

// Send based on time conditions
if ($deadline - $currentTime <= $reminderThreshold) {
    // Send reminder
}
```

### Step 3: Modify the Controller

**Template Code:**
```php
// 1. Before the main business logic, collect items needing notification
$itemsNeedingNotification = [];
foreach ($items as $item) {
    if (/* condition for notification */) {
        $itemsNeedingNotification[] = $item;
    }
}

// 2. Perform main business logic (database updates, etc.)
// ... existing code ...

// 3. After successful operation, send notifications
if (!empty($itemsNeedingNotification)) {
    try {
        $emailService = new \App\Services\EmailService();
        $recipientEmail = /* determine recipient */;
        $emailResult = $emailService->sendYourNotificationMethod($itemsNeedingNotification, $recipientEmail);
        
        if (!$emailResult['success']) {
            log_message('warning', 'Failed to send notification email: ' . $emailResult['message']);
        }
    } catch (\Exception $e) {
        log_message('error', 'Error sending notification email: ' . $e->getMessage());
    }
}
```

### Step 4: Add Method to EmailService

**Template Method:**
```php
/**
 * Send [feature] notification email
 *
 * @param array $items Array of item data
 * @param string $recipientEmail Recipient's email address
 * @return array Result with success status and message
 */
public function sendYourNotificationMethod($items, $recipientEmail)
{
    try {
        if (empty($recipientEmail) || empty($items)) {
            return [
                'success' => false,
                'message' => 'No email address or items provided'
            ];
        }

        // Get related data
        $firstItem = $items[0];
        $organization = $this->orgModel->find($firstItem['org_id']);
        // ... get other related data as needed

        if (!$organization) {
            return [
                'success' => false,
                'message' => 'Required organization data not found'
            ];
        }

        // Prepare email data
        $emailData = [
            'recipient_name' => /* determine name */,
            'items' => $items,
            'organization' => $organization,
            // ... other data needed for template
        ];

        // Generate email content using template
        $emailContent = view('emails/your_notification_template', $emailData);
        
        // Send email
        $result = $this->sendEmail(
            $recipientEmail,
            'Your Email Subject - ' . /* dynamic part */,
            $emailContent,
            $organization
        );

        return $result;

    } catch (\Exception $e) {
        log_message('error', 'Your notification email service error: ' . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Error sending notification: ' . $e->getMessage()
        ];
    }
}
```

### Step 5: Create Email Template

**Template Structure:**
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Your Email Title</title>
    <style>
        /* Use DERS standard styles */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(to bottom left, #F00F00 0%, #D00D00 40%, #000000 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
        }
        /* ... other DERS standard styles ... */
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>Your Email Title</h1>
        </div>

        <div class="content">
            <p>Dear <span class="highlight-text"><?= esc($recipient_name) ?></span>,</p>
            
            <!-- Your email content here -->
            
            <p>Best regards,<br><strong>The DERS Team</strong></p>
        </div>

        <div class="footer">
            <p>This is an automated message, please do not reply to this email.</p>
            <p>&copy; <?= date('Y') ?> DERS. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
```

## Email Template Standards

### Required DERS Styling Elements

1. **Header Gradient:**
   ```css
   background: linear-gradient(to bottom left, #F00F00 0%, #D00D00 40%, #000000 100%);
   ```

2. **Highlight Text:**
   ```css
   .highlight-text {
       color: #F00F00;
       font-weight: bold;
   }
   ```

3. **Accent Borders:**
   ```css
   .accent-border {
       border-left: 4px solid #F00F00;
       padding-left: 15px;
       background: linear-gradient(to right, rgba(240, 15, 0, 0.05) 0%, rgba(0, 0, 0, 0.02) 100%);
   }
   ```

4. **Footer:**
   ```css
   .footer {
       background: linear-gradient(to bottom left, rgba(240, 15, 0, 0.03) 0%, rgba(0, 0, 0, 0.08) 100%);
   }
   ```

### Content Guidelines

- **Greeting:** Always use "Dear [Name]" with red highlighting
- **Signature:** Always end with "Best regards, The DERS Team"
- **Footer:** Standard disclaimer and copyright
- **Tone:** Professional, clear, and helpful
- **Length:** Keep content concise and scannable

## Common Use Cases

### 1. Application Status Notifications

**Trigger:** When application status changes
**Recipients:** Applicants
**Template:** `application_status_notification_template.php`

**Implementation Points:**
- Application submission confirmation
- Application under review
- Application shortlisted
- Application rejected
- Interview invitation

### 2. Interview Scheduling

**Trigger:** When interview is scheduled/rescheduled
**Recipients:** Applicants and interviewers
**Template:** `interview_notification_template.php`

**Implementation Points:**
- Interview scheduled
- Interview rescheduled
- Interview cancelled
- Interview reminder (24 hours before)

### 3. Document Requests

**Trigger:** When additional documents are required
**Recipients:** Applicants
**Template:** `document_request_template.php`

**Implementation Points:**
- Missing documents identified
- Document verification failed
- Additional documents required

### 4. Deadline Reminders

**Trigger:** Time-based (cron job or manual trigger)
**Recipients:** Applicants or staff
**Template:** `deadline_reminder_template.php`

**Implementation Points:**
- Application deadline approaching
- Document submission deadline
- Interview response deadline

### 5. System Notifications

**Trigger:** Administrative actions
**Recipients:** Administrators or users
**Template:** `system_notification_template.php`

**Implementation Points:**
- Account activation
- Password reset
- System maintenance
- Data export ready

## Best Practices

### Code Organization

1. **Single Responsibility:** Each email method handles one type of notification
2. **Error Handling:** Always wrap email calls in try-catch blocks
3. **Logging:** Log all email attempts and failures
4. **Graceful Degradation:** Email failures shouldn't break main functionality

### Performance Considerations

1. **Batch Processing:** For multiple recipients, consider batch sending
2. **Async Processing:** For high-volume notifications, consider queue systems
3. **Rate Limiting:** Respect SMTP server limits
4. **Caching:** Cache organization data for multiple emails

### Security

1. **Input Validation:** Always escape email content
2. **Email Validation:** Validate recipient email addresses
3. **Content Sanitization:** Prevent email injection attacks
4. **Access Control:** Ensure proper authorization before sending

### Maintenance

1. **Template Consistency:** Regular audits of email templates
2. **Link Validation:** Ensure all email links work correctly
3. **Content Updates:** Keep email content current with business changes
4. **Testing:** Regular testing of email functionality

## Testing Guidelines

### Manual Testing Checklist

- [ ] Email triggers correctly under specified conditions
- [ ] Email content displays properly in various email clients
- [ ] All dynamic content populates correctly
- [ ] Links in emails work correctly
- [ ] Email styling matches DERS standards
- [ ] Email failures are logged appropriately
- [ ] Main functionality works even if email fails

### Test Scenarios

1. **Happy Path:** Normal email sending
2. **Invalid Email:** Handling of invalid recipient addresses
3. **SMTP Failure:** Network or server issues
4. **Missing Data:** Incomplete data for email template
5. **Multiple Recipients:** Bulk email scenarios

### Email Client Testing

Test emails in:
- Gmail (web and mobile)
- Outlook (desktop and web)
- Apple Mail
- Mobile email clients

## Troubleshooting

### Common Issues

1. **Emails Not Sending:**
   - Check SMTP configuration
   - Verify network connectivity
   - Check email service logs

2. **Emails Going to Spam:**
   - Verify sender reputation
   - Check email content for spam triggers
   - Ensure proper SPF/DKIM records

3. **Template Rendering Issues:**
   - Check for PHP syntax errors
   - Verify all variables are passed to template
   - Test template with sample data

4. **Performance Issues:**
   - Monitor email sending times
   - Check for database query optimization
   - Consider async processing for bulk emails

### Debugging Steps

1. **Enable Debug Logging:**
   ```php
   log_message('debug', 'Email data: ' . json_encode($emailData));
   ```

2. **Test Email Configuration:**
   ```php
   $emailService = new \App\Services\EmailService();
   $result = $emailService->sendTestEmail('<EMAIL>');
   ```

3. **Check Email Queue:**
   - Monitor email sending status
   - Check for failed email attempts
   - Verify email delivery logs

## Advanced Implementation Examples

### Example 1: Interview Scheduling Notification

**Scenario:** Send email when interview is scheduled for an applicant

**Controller Implementation:**
```php
// In InterviewController::scheduleInterview()
public function scheduleInterview()
{
    $applicationId = $this->request->getPost('application_id');
    $interviewDate = $this->request->getPost('interview_date');
    $interviewTime = $this->request->getPost('interview_time');

    // Get application details before update
    $applicationModel = new \App\Models\AppxApplicationDetailsModel();
    $application = $applicationModel->find($applicationId);

    // Check if this is a new interview (no previous interview_scheduled_at)
    $needsNotification = empty($application['interview_scheduled_at']);

    // Update application with interview details
    $updateData = [
        'interview_date' => $interviewDate,
        'interview_time' => $interviewTime,
        'interview_scheduled_at' => date('Y-m-d H:i:s'),
        'interview_scheduled_by' => $this->session->get('user_id')
    ];

    if ($applicationModel->update($applicationId, $updateData)) {
        // Send notification if this is a new interview
        if ($needsNotification) {
            try {
                $emailService = new \App\Services\EmailService();
                $emailResult = $emailService->sendInterviewNotification($application, $application['email_address']);

                if (!$emailResult['success']) {
                    log_message('warning', 'Failed to send interview notification: ' . $emailResult['message']);
                }
            } catch (\Exception $e) {
                log_message('error', 'Error sending interview notification: ' . $e->getMessage());
            }
        }

        return redirect()->back()->with('success', 'Interview scheduled successfully.');
    }
}
```

**EmailService Method:**
```php
public function sendInterviewNotification($application, $applicantEmail)
{
    try {
        if (empty($applicantEmail)) {
            return ['success' => false, 'message' => 'No email address provided'];
        }

        $organization = $this->orgModel->find($application['org_id']);
        $position = $this->positionsModel->find($application['position_id']);
        $exercise = $this->exerciseModel->find($application['exercise_id']);

        $emailData = [
            'applicant_name' => trim($application['first_name'] . ' ' . $application['last_name']),
            'application' => $application,
            'organization' => $organization,
            'position' => $position,
            'exercise' => $exercise,
            'interview_date' => date('l, F j, Y', strtotime($application['interview_date'])),
            'interview_time' => date('g:i A', strtotime($application['interview_time']))
        ];

        $emailContent = view('emails/interview_notification_template', $emailData);

        return $this->sendEmail(
            $applicantEmail,
            'Interview Scheduled - ' . $position['designation'],
            $emailContent,
            $organization
        );

    } catch (\Exception $e) {
        log_message('error', 'Interview notification error: ' . $e->getMessage());
        return ['success' => false, 'message' => 'Error sending notification: ' . $e->getMessage()];
    }
}
```

### Example 2: Bulk Status Update Notifications

**Scenario:** Send notifications when multiple applications are updated

**Controller Implementation:**
```php
// In ApplicationController::bulkUpdateStatus()
public function bulkUpdateStatus()
{
    $applicationIds = $this->request->getPost('application_ids');
    $newStatus = $this->request->getPost('status');

    $applicationModel = new \App\Models\AppxApplicationDetailsModel();
    $applicationsNeedingNotification = [];

    // Check which applications need notifications
    foreach ($applicationIds as $id) {
        $application = $applicationModel->find($id);
        if ($application && $application['status'] !== $newStatus) {
            $applicationsNeedingNotification[] = $application;
        }
    }

    // Perform bulk update
    $updateData = ['status' => $newStatus, 'updated_at' => date('Y-m-d H:i:s')];
    $applicationModel->update($applicationIds, $updateData);

    // Send notifications
    if (!empty($applicationsNeedingNotification)) {
        $this->sendBulkStatusNotifications($applicationsNeedingNotification, $newStatus);
    }

    return redirect()->back()->with('success', 'Applications updated successfully.');
}

private function sendBulkStatusNotifications($applications, $newStatus)
{
    $emailService = new \App\Services\EmailService();

    foreach ($applications as $application) {
        try {
            $emailResult = $emailService->sendStatusChangeNotification($application, $newStatus);
            if (!$emailResult['success']) {
                log_message('warning', "Failed to send status notification for application {$application['id']}: " . $emailResult['message']);
            }
        } catch (\Exception $e) {
            log_message('error', "Error sending status notification for application {$application['id']}: " . $e->getMessage());
        }
    }
}
```

### Example 3: Time-Based Reminder System

**Scenario:** Send deadline reminders using cron jobs

**Command Implementation:**
```php
// Create app/Commands/SendDeadlineReminders.php
<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class SendDeadlineReminders extends BaseCommand
{
    protected $group = 'DERS';
    protected $name = 'ders:send-reminders';
    protected $description = 'Send deadline reminder emails';

    public function run(array $params)
    {
        $exerciseModel = new \App\Models\ExerciseModel();
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();
        $emailService = new \App\Services\EmailService();

        // Find exercises with deadlines in next 3 days
        $upcomingDeadlines = $exerciseModel->where('publish_date_to >=', date('Y-m-d'))
                                          ->where('publish_date_to <=', date('Y-m-d', strtotime('+3 days')))
                                          ->findAll();

        foreach ($upcomingDeadlines as $exercise) {
            // Get applications that haven't been submitted yet
            $incompleteApplications = $applicationModel->where('exercise_id', $exercise['id'])
                                                      ->where('status', 'draft')
                                                      ->findAll();

            foreach ($incompleteApplications as $application) {
                try {
                    $emailResult = $emailService->sendDeadlineReminder($application, $exercise);
                    if ($emailResult['success']) {
                        CLI::write("Reminder sent to {$application['email_address']}", 'green');
                    } else {
                        CLI::write("Failed to send reminder to {$application['email_address']}: {$emailResult['message']}", 'red');
                    }
                } catch (\Exception $e) {
                    CLI::write("Error sending reminder: " . $e->getMessage(), 'red');
                }
            }
        }

        CLI::write('Deadline reminders process completed.', 'yellow');
    }
}
```

**Cron Job Setup:**
```bash
# Add to crontab to run daily at 9 AM
0 9 * * * cd /path/to/ders && php spark ders:send-reminders
```

## Email Template Variations

### Conditional Content Blocks

```html
<!-- Show different content based on status -->
<?php if ($status === 'shortlisted'): ?>
    <div class="success-box">
        <h2>Congratulations!</h2>
        <p>Your application has been shortlisted for the next stage.</p>
    </div>
<?php elseif ($status === 'rejected'): ?>
    <div class="info-box">
        <h2>Application Update</h2>
        <p>Thank you for your interest. Unfortunately, your application was not successful this time.</p>
    </div>
<?php endif; ?>
```

### Dynamic Lists

```html
<!-- Multiple positions or items -->
<div class="items-list">
    <h3>Affected Items</h3>
    <?php foreach ($items as $item): ?>
        <div class="item-card">
            <div class="item-title"><?= esc($item['title']) ?></div>
            <div class="item-details"><?= esc($item['details']) ?></div>
            <?php if (!empty($item['action_required'])): ?>
                <div class="action-required">Action Required: <?= esc($item['action_required']) ?></div>
            <?php endif; ?>
        </div>
    <?php endforeach; ?>
</div>
```

### Call-to-Action Buttons

```html
<!-- Action buttons -->
<div style="text-align: center; margin: 30px 0;">
    <a href="<?= base_url('applicant/dashboard') ?>" class="button">View Dashboard</a>
    <a href="<?= base_url('applicant/application/' . $application['id']) ?>" class="button-secondary">View Application</a>
</div>

<style>
.button {
    display: inline-block;
    padding: 15px 30px;
    background: linear-gradient(to bottom left, #F00F00 0%, #D00D00 60%, #000000 100%);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    margin: 10px;
    font-weight: bold;
    font-size: 16px;
}

.button-secondary {
    display: inline-block;
    padding: 15px 30px;
    background: #6c757d;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    margin: 10px;
    font-weight: bold;
    font-size: 16px;
}
</style>
```

## Integration with Existing Features

### Database Schema Considerations

When adding email notifications, consider adding these fields to relevant tables:

```sql
-- Add to applications table
ALTER TABLE appx_application_details ADD COLUMN last_email_sent_at DATETIME NULL;
ALTER TABLE appx_application_details ADD COLUMN email_notifications_enabled TINYINT(1) DEFAULT 1;

-- Add to exercises table
ALTER TABLE exercises ADD COLUMN reminder_emails_enabled TINYINT(1) DEFAULT 1;
ALTER TABLE exercises ADD COLUMN reminder_days_before INT DEFAULT 3;

-- Create email log table
CREATE TABLE email_notifications_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    recipient_email VARCHAR(255) NOT NULL,
    email_type VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    sent_at DATETIME NOT NULL,
    success TINYINT(1) NOT NULL,
    error_message TEXT NULL,
    related_table VARCHAR(100) NULL,
    related_id INT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Configuration Management

Create email configuration options:

```php
// In app/Config/Email.php or custom config
class EmailNotifications extends BaseConfig
{
    public $enableNotifications = true;
    public $enablePrescreeningNotifications = true;
    public $enableInterviewNotifications = true;
    public $enableStatusChangeNotifications = true;
    public $enableDeadlineReminders = true;

    public $reminderDaysBefore = 3;
    public $maxEmailsPerHour = 100;
    public $retryFailedEmails = true;
    public $retryAttempts = 3;
}
```

## Conclusion

This comprehensive implementation guide provides everything needed to add email notification features throughout the DERS system. The examples demonstrate real-world scenarios and the modular approach ensures consistency and maintainability.

Key takeaways:
- **Consistency:** Use standardized patterns and templates
- **Reliability:** Implement proper error handling and logging
- **Flexibility:** Design for easy extension and modification
- **Performance:** Consider bulk operations and async processing
- **User Experience:** Provide clear, actionable email content

By following this guide, developers can efficiently implement email notifications that enhance the user experience while maintaining system stability and performance.

---

**Document Version:** 1.0
**Last Updated:** 2025-01-21
**Author:** DERS Development Team
**Reference Implementation:** Prescreening Notification Feature
